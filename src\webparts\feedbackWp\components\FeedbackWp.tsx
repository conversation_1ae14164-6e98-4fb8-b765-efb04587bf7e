import * as React from "react";
import { useEffect, useMemo, useState } from "react";
import type { IFeedbackWpProps } from "./IFeedbackWpProps";
import { getSP } from "./pnpConfig";
import type { SPFI } from "@pnp/sp";

import "@pnp/sp/site-users/web"; // enables web.currentUser()
import "@pnp/sp/webs";

import {
  Stack,
  Text,
  PrimaryButton,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Rating as FluentRating,
  TextField,
  Dropdown,
  IDropdownOption,
  ChoiceGroup,
  IChoiceGroupOption,
} from "@fluentui/react";

const LIST_FEEDBACK_INVITES = "FeedbackInvites";
const LIST_FEEDBACK_RESPONSES = "FeedbackResponses";
const LIST_TRAINING_BOOKING = "TrainingBookings";

type View = "loading" | "invalid" | "forbidden" | "form" | "thanks" | "already";
type YesNo = "Yes" | "No";

const yesNoOptions: IChoiceGroupOption[] = [
  { key: "Yes", text: "Yes" },
  { key: "No", text: "No" },
];

const depthOptions: IDropdownOption[] = [
  { key: "Too basic", text: "Too basic" },
  { key: "Just right", text: "Just right" },
  { key: "Too advanced", text: "Too advanced" },
];

const paceOptions: IDropdownOption[] = [
  { key: "Too slow", text: "Too slow" },
  { key: "Just right", text: "Just right" },
  { key: "Too fast", text: "Too fast" },
];

const durationOptions: IDropdownOption[] = [
  { key: "Too short", text: "Too short" },
  { key: "Just right", text: "Just right" },
  { key: "Too long", text: "Too long" },
];

const materialsOptions: IDropdownOption[] = [
  { key: "Agree", text: "Agree" },
  { key: "Neutral", text: "Neutral" },
  { key: "Disagree", text: "Disagree" },
];

// Utility: safe error message extraction
function toMessage(e: unknown): string {
  return e instanceof Error ? e.message : String(e);
}

export default function FeedbackWp(
  props: IFeedbackWpProps
): React.ReactElement {
  const sp: SPFI = getSP(); // PnP already initialized in onInit()

  const [view, setView] = useState<View>("loading");
  const [error, setError] = useState<string>("");

  const [inviteId, setInviteId] = useState<number | null>(null);
  const [bookingId, setBookingId] = useState<number | null>(null);
  const [inviteEmail, setInviteEmail] = useState<string>("");
  const [, setResponseId] = useState<number | null>(null);

  // Header (from TrainingBookings)
  const [courseTitle, setCourseTitle] = useState<string>("");
  const [trainingDate, setTrainingDate] = useState<string>("");
  const [trainerName, setTrainerName] = useState<string>("");
  const [sessionChoice, setSessionChoice] = useState<string>("");

  // Top-level fields in FeedbackResponses
  const [rating, setRating] = useState<number>(0);
  const [comments, setComments] = useState<string>("");

  // Extended Qs (go to AnswersJson)
  const [contentRelevant, setContentRelevant] = useState<YesNo | "">("");
  const [objectivesClear, setObjectivesClear] = useState<number>(0);
  const [contentDepth, setContentDepth] = useState<string>("");

  const [trainerClarity, setTrainerClarity] = useState<number>(0);
  const [trainerKnowledge, setTrainerKnowledge] = useState<number>(0);
  const [participation, setParticipation] = useState<YesNo | "">("");

  const [pace, setPace] = useState<string>("");
  const [duration, setDuration] = useState<string>("");
  const [materialsUseful, setMaterialsUseful] = useState<string>("");

  const [applyConfidence, setApplyConfidence] = useState<number>(0);
  const [skillsImmediate, setSkillsImmediate] = useState<string>("");
  const [needsFocus, setNeedsFocus] = useState<string>("");
  const [overallSatisfaction, setOverallSatisfaction] = useState<number>(0);
  const [recommend, setRecommend] = useState<YesNo | "">("");

  const token = useMemo<string | null>(() => {
    try {
      return new URL(window.location.href).searchParams.get("token");
    } catch {
      return null;
    }
  }, []);

  useEffect((): void => {
    const run = async (): Promise<void> => {
      try {
        if (!token) {
          setView("invalid");
          return;
        }

        // 1) Invite by token
        const invites = await sp.web.lists
          .getByTitle(LIST_FEEDBACK_INVITES)
          .items.select("Id,Token,BookingId,TraineeEmail,Used,SubmittedItemId")
          .filter(`Token eq '${token.replace(/'/g, "''")}'`)
          .top(1)();

        const invite = invites?.[0];
        if (!invite) {
          setView("invalid");
          return;
        }

        // 2) Current user must match invite.TraineeEmail
        const me = await sp.web.currentUser();
        const meEmail = (me.Email || "").toLowerCase();
        const invitedEmail = (invite.TraineeEmail || "").toLowerCase();
        if (!meEmail || meEmail !== invitedEmail) {
          setView("forbidden");
          return;
        }

        setInviteId(invite.Id);
        setInviteEmail(invite.TraineeEmail);
        setBookingId(invite.BookingId);

        // 3) Load TrainingBookings header
        if (invite.BookingId) {
          const booking = await sp.web.lists
            .getByTitle(LIST_TRAINING_BOOKING)
            .items.getById(invite.BookingId)
            .select("Title,TrainingDate,Session,Trainer/Title")
            .expand("Trainer")();

          setCourseTitle(booking?.Title || "");
          setTrainingDate(booking?.TrainingDate || "");
          setSessionChoice(booking?.Session || "");
          setTrainerName(booking?.Trainer?.Title || "");
        }

        // 4) Already submitted?
        if (invite.Used || invite.SubmittedItemId) {
          setResponseId(invite.SubmittedItemId || null);
          setView("already");
          return;
        }

        // 5) Race check: existing response?
        const existing = await sp.web.lists
          .getByTitle(LIST_FEEDBACK_RESPONSES)
          .items.select("Id")
          .filter(
            `BookingId eq ${
              invite.BookingId
            } and TraineeEmail eq '${invite.TraineeEmail.replace(/'/g, "''")}'`
          )
          .top(1)();

        if (existing?.[0]?.Id) {
          setResponseId(existing[0].Id);
          await sp.web.lists
            .getByTitle(LIST_FEEDBACK_INVITES)
            .items.getById(invite.Id)
            .update({ Used: true, SubmittedItemId: existing[0].Id });
          setView("already");
          return;
        }

        setView("form");
      } catch (e) {
        setError(e instanceof Error ? e.message : String(e));
        setView("invalid");
      }
    };

    run().catch((e) => {
      // final safety: ensures no-floating-promises and logs unexpected errors
      setError(e instanceof Error ? e.message : String(e));
      setView("invalid");
    });
  }, [token, sp]);

  async function handleSubmit(): Promise<void> {
    try {
      if (!inviteId || !bookingId || !inviteEmail) return;

      const answers = {
        ContentRelevance: contentRelevant || null,
        ObjectivesClear: objectivesClear,
        ContentDepth: contentDepth || null,
        TrainerClarity: trainerClarity,
        TrainerKnowledge: trainerKnowledge,
        TrainerEncouragedParticipation: participation || null,
        Pace: pace || null,
        Duration: duration || null,
        MaterialsUseful: materialsUseful || null,
        ConfidenceToApply: applyConfidence,
        SkillsToUseImmediately: skillsImmediate || "",
        TopicsNeedingMoreFocus: needsFocus || "",
        OverallSatisfaction: overallSatisfaction,
        Recommend: recommend || null,
        CourseTitle: courseTitle || "",
        TrainingDate: trainingDate || "",
        TrainerName: trainerName || "",
        Session: sessionChoice || "",
      };

      const add = await sp.web.lists
        .getByTitle(LIST_FEEDBACK_RESPONSES)
        .items.add({
          Title: `Response - ${courseTitle || "Training"} - ${inviteEmail}`,
          BookingId: bookingId,
          TraineeEmail: inviteEmail,
          Rating: rating,
          Comments: comments,
          AnswersJson: JSON.stringify(answers),
        });

      await sp.web.lists
        .getByTitle(LIST_FEEDBACK_INVITES)
        .items.getById(inviteId)
        .update({ Used: true, SubmittedItemId: add.data.Id });

      setResponseId(add.data.Id);
      setView("thanks");
    } catch (e: unknown) {
      setError(toMessage(e));
    }
  }

  // ---------- UI ----------
  const Shell = (children: React.ReactNode): JSX.Element => (
    <div style={{ maxWidth: 760, margin: "0 auto", padding: 16 }}>
      <div
        style={{
          background: "#fff",
          border: "1px solid #e5e5e5",
          borderRadius: 12,
          padding: 20,
          boxShadow: "0 4px 14px rgba(0,0,0,0.06)",
        }}
      >
        <Header
          title={courseTitle || "Training Feedback"}
          meta={[
            trainingDate ? new Date(trainingDate).toLocaleString() : "",
            sessionChoice ? `Session: ${sessionChoice}` : "",
            trainerName ? `Trainer: ${trainerName}` : "",
          ]
            .filter(Boolean)
            .join(" • ")}
        />
        {children}
      </div>
    </div>
  );

  if (view === "loading") return Shell(<Text>Loading…</Text>);
  if (view === "invalid")
    return Shell(
      <MessageBar messageBarType={MessageBarType.error}>
        Invalid or missing link. {error}
      </MessageBar>
    );
  if (view === "forbidden")
    return Shell(
      <MessageBar messageBarType={MessageBarType.severeWarning}>
        This link isn’t for your account.
      </MessageBar>
    );

  if (view === "form") {
    return Shell(
      <Stack tokens={{ childrenGap: 18 }}>
        {/* Content */}
        <Text variant="large" styles={{ root: { fontWeight: 600 } }}>
          Training Content
        </Text>
        <ChoiceGroup
          label="Was the content relevant to your work?"
          options={yesNoOptions}
          selectedKey={contentRelevant || undefined}
          onChange={(_, opt) => setContentRelevant((opt?.key as YesNo) ?? "")}
        />
        <div>
          <Text>The training objectives were clearly defined.</Text>
          <FluentRating
            max={5}
            rating={objectivesClear}
            onChange={(_, n) => setObjectivesClear(n ?? 0)}
          />
        </div>
        <Dropdown
          label="Content depth"
          options={depthOptions}
          selectedKey={contentDepth || undefined}
          onChange={(_, opt) => setContentDepth((opt?.key as string) ?? "")}
        />

        {/* Trainer */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Trainer & Delivery
        </Text>
        <div>
          <Text>The trainer explained concepts clearly and effectively.</Text>
          <FluentRating
            max={5}
            rating={trainerClarity}
            onChange={(_, n) => setTrainerClarity(n ?? 0)}
          />
        </div>
        <div>
          <Text>The trainer was knowledgeable about the subject.</Text>
          <FluentRating
            max={5}
            rating={trainerKnowledge}
            onChange={(_, n) => setTrainerKnowledge(n ?? 0)}
          />
        </div>
        <ChoiceGroup
          label="The trainer encouraged questions and participation."
          options={yesNoOptions}
          selectedKey={participation || undefined}
          onChange={(_, opt) => setParticipation((opt?.key as YesNo) ?? "")}
        />

        {/* Experience */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Training Experience
        </Text>
        <Dropdown
          label="Pace of the training"
          options={paceOptions}
          selectedKey={pace || undefined}
          onChange={(_, opt) => setPace((opt?.key as string) ?? "")}
        />
        <Dropdown
          label="Duration of the session"
          options={durationOptions}
          selectedKey={duration || undefined}
          onChange={(_, opt) => setDuration((opt?.key as string) ?? "")}
        />
        <Dropdown
          label="Training materials/slides were useful"
          options={materialsOptions}
          selectedKey={materialsUseful || undefined}
          onChange={(_, opt) => setMaterialsUseful((opt?.key as string) ?? "")}
        />

        {/* Practical Application */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Practical Application
        </Text>
        <div>
          <Text>I feel confident to apply what I learned.</Text>
          <FluentRating
            max={5}
            rating={applyConfidence}
            onChange={(_, n) => setApplyConfidence(n ?? 0)}
          />
        </div>
        <TextField
          label="Which skills or knowledge will you use immediately?"
          multiline
          rows={4}
          value={skillsImmediate}
          onChange={(_, v) => setSkillsImmediate(v ?? "")}
        />
        <TextField
          label="What topics need more focus or deeper explanation?"
          multiline
          rows={4}
          value={needsFocus}
          onChange={(_, v) => setNeedsFocus(v ?? "")}
        />

        {/* Overall */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Overall
        </Text>
        <div>
          <Text>Overall satisfaction with this training</Text>
          <FluentRating
            max={5}
            rating={overallSatisfaction}
            onChange={(_, n) => setOverallSatisfaction(n ?? 0)}
          />
        </div>
        <ChoiceGroup
          label="Would you recommend this training to colleagues?"
          options={yesNoOptions}
          selectedKey={recommend || undefined}
          onChange={(_, opt) => setRecommend((opt?.key as YesNo) ?? "")}
        />
        <TextField
          label="Additional comments (optional)"
          multiline
          rows={5}
          value={comments}
          onChange={(_, v) => setComments(v ?? "")}
        />

        {/* Quick overall star (top-level Rating column) */}
        <div>
          <Text>Quick overall rating</Text>
          <FluentRating
            max={5}
            rating={rating}
            onChange={(_, n) => setRating(n ?? 0)}
          />
        </div>

        {error && (
          <MessageBar messageBarType={MessageBarType.error}>{error}</MessageBar>
        )}

        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <PrimaryButton text="Submit" onClick={handleSubmit} />
          <DefaultButton
            text="Back to portal"
            onClick={(): void => {
              window.location.href = "/sites/<site>";
            }}
          />
        </Stack>
      </Stack>
    );
  }

  if (view === "thanks") {
    return Shell(
      <MessageBar messageBarType={MessageBarType.success} isMultiline>
        Thanks! Your feedback has been submitted. You can close this page now.
      </MessageBar>
    );
  }

  // Already submitted
  return Shell(
    <Stack tokens={{ childrenGap: 12 }}>
      <MessageBar messageBarType={MessageBarType.success}>
        Thanks! You’ve already submitted this feedback for this booking.
      </MessageBar>
      <DefaultButton
        text="Back to portal"
        onClick={(): void => {
          window.location.href = "/sites/<site>";
        }}
      />
    </Stack>
  );
}

function Header({
  title,
  meta,
}: {
  title: string;
  meta?: string;
}): JSX.Element {
  return (
    <Stack tokens={{ childrenGap: 4 }} styles={{ root: { marginBottom: 12 } }}>
      <Text variant="xLargePlus" styles={{ root: { fontWeight: 600 } }}>
        {title}
      </Text>
      {meta && (
        <Text variant="smallPlus" styles={{ root: { color: "#666" } }}>
          {meta}
        </Text>
      )}
    </Stack>
  );
}
